{
  "compilerOptions": {
    "module": "commonjs",
    "target": "ES2022",
    "outDir": "out",
    "lib": ["ES2022", "dom", "es6", "es5", "dom.iterable", "scripthost"],
    "sourceMap": true,
    "rootDirs": ["src", "../core"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true /* enable all strict tsc:watching options */,
    /* Additional Checks */
    // "noImplicitReturns": true, /* Report error when not all code paths in function return a value. */
    // "noFallthroughCasesInSwitch": true, /* Report errors for fallthrough cases in switch statement. */
    // "noUnusedParameters": true,  /* Report errors on unused parameters. */
    "esModuleInterop": true /* Enables emit interoperability between CommonJS and ES Modules via creation of namespace objects for all imports. Implies 'allowSyntheticDefaultImports'. */,
    "resolveJsonModule": true /* Enable importing .json files */,
    "types": ["jest"]
  },
  "include": ["src/**/*", "test/**/*"]
}
