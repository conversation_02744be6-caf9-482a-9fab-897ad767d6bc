# Cerebras Inference

import TabItem from "@theme/TabItem";
import Tabs from "@theme/Tabs";

Cerebras Inference uses specialized silicon to provides fast inference.

1. Create an account in the portal [here](https://cloud.cerebras.ai/).
2. Create and copy the API key for use in Continue.
3. Update your Continue config file:

<Tabs groupId="config-example">
  <TabItem value="yaml" label="YAML">
  ```yaml title="config.yaml"
  models:
    - name: Cerebras Llama 3.1 70B
      provider: cerebras
      model: llama3.1-70b
      apiKey:  <YOUR_CEREBRAS_API_KEY>
  ```
  </TabItem>
  <TabItem value="json" label="JSON">
  ```json title="config.json"
  {
    "models": [
      {
        "title": "Cerebras Llama 3.1 70B",
        "provider": "cerebras",
        "model": "llama3.1-70b",
        "apiKey": "<YOUR_CEREBRAS_API_KEY>"
      }
    ]
  }
  ```
  </TabItem>
</Tabs>
