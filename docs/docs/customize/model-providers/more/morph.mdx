import Tabs from "@theme/Tabs";
import TabItem from "@theme/TabItem";

# Morph

Morph provides a fast apply model that helps you quickly and accurately apply code changes from chat suggestions to your files. It's optimized for speed and precision when integrating generated code into your existing codebase. You can sign up for Morph's generous free tier [here](https://morphllm.com/dashboard). Then, update your configuration file as follows:

<Tabs groupId="config-example">
   <TabItem value="yaml" label="YAML">
   ```yaml title="config.yaml"
   models:
     - uses: morphllm/morph-v0
       with:
         MORPH_API_KEY: ${{ secrets.MORPH_API_KEY }}
   ```
   </TabItem>
   <TabItem value="json" label="JSON">
   ```json title="config.json"
   {
     "models": [
       {
         "title": "Morph Fast Apply",
         "provider": "openai",
         "model": "morph-v0",
         "apiKey": "<YOUR_MORPH_API_KEY>",
         "apiBase": "https://api.morphllm.com/v1/",
         "roles": ["apply", "chat"],
         "promptTemplates": {
           "apply": "<code>{{{ original_code }}}</code>\n<update>{{{ new_code }}}</update>"
         }
       }
     ]
   }
   ```
   </TabItem>
</Tabs>
