import TabItem from "@theme/TabItem";
import Tabs from "@theme/Tabs";

# Flowise

[Flowise](https://flowiseai.com/) is a low-code/no-code drag & drop tool with the aim to make it easy for people to visualize and build LLM apps. Continue can then be configured to use the `Flowise` LLM class, like the example here:

<Tabs groupId="config-example">
  <TabItem value="yaml" label="YAML">
  ```yaml title="config.yaml"
  models:
    - name: Flowise
      provider: flowise
      model: <MODEL>
      apiBase: <API_BASE>
  ```
  </TabItem>
  <TabItem value="json" label="JSON">
  ```json title="config.json"
  {
    "models": [
      {
        "provider": "flowise",
        "title": "Flowise",
        "model": "<MODEL>",
        "apiBase": "<API_BASE>"
      }
    ]
  }
  ```
  </TabItem>
</Tabs>

[View the source](https://github.com/continuedev/continue/blob/main/core/llm/llms/Flowise.ts)
