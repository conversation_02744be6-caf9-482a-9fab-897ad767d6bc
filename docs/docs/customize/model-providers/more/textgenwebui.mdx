import TabItem from "@theme/TabItem";
import Tabs from "@theme/Tabs";

# TextGenWebUI

TextGenWebUI is a comprehensive, open-source language model UI and local server. You can set it up with an OpenAI-compatible server plugin, and then configure it like this:

<Tabs groupId="config-example">
    <TabItem value="yaml" label="YAML">
    ```yaml title="config.yaml"
    models:
      - name: Text Generation WebUI
        provider: text-gen-webui
        apiBase: http://localhost:5000/v1
        model: MODEL_NAME
    ```
    </TabItem>
    <TabItem value="json" label="JSON">
    ```json title="config.json"
    {
      "models": [
        {
          "title": "Text Generation WebUI",
          "provider": "text-gen-webui",
          "apiBase": "http://localhost:5000/v1",
          "model": "MODEL_NAME"
        }
      ]
    }
    ```
    </TabItem>
</Tabs>
