---
title: Context Blocks
sidebar_label: Context
description: Determine what internal information your AI assistant can access
keywords: [context, blocks, repositories, integration, knowledge base]
sidebar_position: 2
---

These blocks determine what internal information your AI assistant can access:

- **Connect to source repositories** and issues, CI/CD systems, wikis, and knowledge bases
- **Determine the scope** of what the AI can "see" when providing assistance
- **Customize** to match your specific development environment
- **Include proprietary code patterns** unique to your organization
- **Integrate data sources** to connect to any internal system with valuable context

![Context Blocks Overview](/img/context-blocks-overview.png)

## Learn More

Learn more about context providers [here](../reference.md#context), and check out [this guide](../customize/tutorials/build-your-own-context-provider.mdx) to creating your own custom context provider.
