---
title: Prompt Blocks
sidebar_label: Prompt
description: Specialized instructions that shape how models respond
keywords: [prompts, blocks, instructions, guidance, code review]
sidebar_position: 6
---

These are the specialized instructions that shape how models respond:

- **Define interaction patterns** for specific tasks or frameworks
- **Encode domain expertise** for particular technologies
- **Ensure consistent guidance** aligned with organizational practices
- **Can be shared and reused** across multiple assistants
- **Act as automated code reviewers** that ensure consistency across teams

![Prompt Blocks Overview](/img/prompts-blocks-overview.png)

## Learn More

Prompt blocks have the same syntax as [prompt files](../customize/deep-dives/prompts.md). The `config.yaml` spec for `prompts` can be found [here](../reference.md#prompts).
