---
title: Autocomplete
description: Autocomplete \- how to use it
sidebar_label: How to use it
keywords: [autocomplete]
sidebar_position: 1
---

![autocomplete](/img/autocomplete.gif)

## How to use it

Autocomplete provides inline code suggestions as you type. To enable it, simply click the "Continue" button in the status bar at the bottom right of your IDE or ensure the "Enable Tab Autocomplete" option is checked in your IDE settings.

### Accepting a full suggestion

Accept a full suggestion by pressing <kbd>Tab</kbd>

### Rejecting a full suggestion

Reject a full suggestion with <kbd>Esc</kbd>

### Partially accepting a suggestion

For more granular control, use <kbd>cmd/ctrl</kbd> + <kbd>→</kbd> to accept parts of the suggestion word-by-word.
