---
title: Introduction
sidebar_label: Introduction
description: Introduction to blocks
keywords: [blocks, introduction, basics]
---

# Introduction

Blocks are the components you use to build a custom AI code assistant. These include [models](./block-types.md#models), [rules](./block-types.md#rules), [context providers](./block-types.md#context), [prompts](./block-types.md#prompts), [docs](./block-types.md#docs), [data destinations](./block-types.md#data), and [MCP servers](./block-types.md#mcp-servers).

Blocks follow the [`config.yaml`](../../reference.md) format. When combined with other blocks into a complete `config.yaml`, they form a custom AI code assistant.
