---
title: Edit an Assistant
sidebar_label: Edit an Assistant
description: How to edit a custom AI coding assistant.
keywords: [assistants, creation, guide]
---

# Edit an Assistant

New versions of an assistant can be created and published using the sidebar.

![Remix Assistant Button](/img/hub/assistant-create-sidebar.png)

First, select an assistant from the dropdown at the top.

While editing an assistant, you can explore the hub and click "Add Block" from a block page to add it to your assistant.

For blocks that require secret values like API keys, you will see a small notification on the block's tile in the sidebar that will indicate if action is needed.

To delete a block, click the trash icon.

If a block you want to use does not exist yet, you can [create a new block](../blocks/create-a-block.md).

When you are done editing, click "Publish" to publish a new version of the assistant.

Click "Open VS Code" or "Open JetBrains" to open your IDE for using the assistant.