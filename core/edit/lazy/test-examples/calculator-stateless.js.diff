class Calculator {
  constructor() {
    this.result = 0;
  }

  add(number) {
    this.result += number;
    return this;
  }

  subtract(number) {
    this.result -= number;
    return this;
  }

  multiply(number) {
    this.result *= number;
    return this;
  }

  divide(number) {
    if (number === 0) {
      throw new Error("Cannot divide by zero");
    }
    this.result /= number;
    return this;
  }

  getResult() {
    return this.result;
  }

  reset() {
    this.result = 0;
    return this;
  }
}

---

class Calculator {
  // ... existing code ...

  static add(a, b) {
    return a + b;
  }

  static subtract(a, b) {
    return a - b;
  }

  static multiply(a, b) {
    return a * b;
  }

  static divide(a, b) {
    if (b === 0) {
      throw new Error("Cannot divide by zero");
    }
    return a / b;
  }
}

---

class Calculator {
  constructor() {
    this.result = 0;
  }

-   add(number) {
-     this.result += number;
-     return this;
+   static add(a, b) {
+     return a + b;
  }

-   subtract(number) {
-     this.result -= number;
-     return this;
+   static subtract(a, b) {
+     return a - b;
  }

-   multiply(number) {
-     this.result *= number;
-     return this;
+   static multiply(a, b) {
+     return a * b;
  }

-   divide(number) {
-     if (number === 0) {
+   static divide(a, b) {
+     if (b === 0) {
      throw new Error("Cannot divide by zero");
    }
-     this.result /= number;
-     return this;
+     return a / b;
  }
- 
-   getResult() {
-     return this.result;
-   }
- 
-   reset() {
-     this.result = 0;
-     return this;
-   }
}