{"version": 3, "file": "tokenizers.d.ts", "sourceRoot": "", "sources": ["../src/tokenizers.js"], "names": [], "mappings": ";;;;AAoQA;;;;GAIG;AACH;IA0BI;;;;;;OAMG;IACH,gDAHa,cAAc,CAqB1B;IAlDD;;;OAGG;IACH,yBAmBC;IAjBG,YAAoB;IAEpB,uBAAuB;IACvB,OADW,MAAM,EAAE,CACJ;IAEf;;;OAGG;IACH,eAFU,IAAI,MAAM,EAAE,MAAM,CAAC,CAEC;IAE9B,kBAA6B;IAC7B,eAA0B;IAC1B,wBAAmC;IAEnC,uFAAuF;IACvF,UADW,OAAO,CAC2B;IA8BjD;;;;OAIG;IACH,cAHW,MAAM,EAAE,GACN,MAAM,EAAE,CASpB;IAED;;;;;OAKG;IACH,eAJW,MAAM,EAAE,GACN,MAAM,EAAE,CAKpB;IAED;;;;OAIG;IACH,8BAHW,MAAM,EAAE,GACN,MAAM,EAAE,CAIpB;IAED;;;;OAIG;IACH,2BAHW,MAAM,EAAE,GACN,MAAM,EAAE,CAIpB;CACJ;;;;;AAk/DD;IAsHI;;;;;;;;OAQG;IACH,sDANW,MAAM,kFACN,0BAA0B,GAGxB,QAAQ,mBAAmB,CAAC,CAsBxC;IA9ID;;;;OAIG;IACH,sDAiFC;IA1FD,+BAA8B;IAE9B,+BAAmO;IAU/N,uBAAwC;IAGxC,uBAAiE;IACjE,4BAAyE;IACzE,sBAA4E;IAC5E,8BAA4E;IAC5E,iBAAwD;IAGxD,sBAAwB;IACxB,0BAAyB;IAEzB,2BAA2B;IAC3B,cADW,UAAU,EAAE,CACD;IAetB,+BAAgF;IAgBhF,2BAEQ;IAGR,mBAA6C;IAC7C,sBAAkE;IAElE,kBAAwD;IACxD,qBAAgE;IAEhE,kBAA2C;IAC3C,qBAAgE;IAEhE,kBAA4D;IAC5D,qBAAgE;IAEhE,sBAAwD;IAExD,6HAA6H;IAC7H,cADW,OAAO,CAC8B;IAEhD,kCAAwF;IACxF,oCAA6F;IAG7F,6BAA6B;IAC7B,cADW,OAAO,GAAC,MAAM,CACE;IAE3B,gBAAmB;IAEnB,mBAA0D;IAC1D,wCAAyC;IAG7C;;;;;OAKG;IACH,kBAJc,MAAM,KACP,MAAM,GAAC,IAAI,CAoBvB;IAiCD;;;;;;;OAOG;IAEH;;;;;;;;;;;OAWG;IACH,YAVW,MAAM,GAAC,MAAM,EAAE;QAEW,SAAS,GAAnC,MAAM,GAAC,MAAM,EAAE;QACgB,OAAO,GAAtC,OAAO,GAAC,YAAY;QACF,kBAAkB,GAApC,OAAO;QACW,UAAU,GAA5B,OAAO;QACU,UAAU,GAA3B,MAAM;QACY,aAAa,GAA/B,OAAO;;;;;;;;;;;;;;MAyJjB;IAED;;;;;OAKG;IACH,mBAHW,MAAM,GAAC,IAAI,GACT,MAAM,EAAE,GAAC,IAAI,CAsCzB;IAED;;;;;;;;;OASG;IACH,qBAqBC;IAED;;;;;;;;OAQG;IACH,aANW,MAAM,cACN,MAAM,GAAC,IAAI;QAEO,kBAAkB,GAApC,OAAO;QACL,MAAM,EAAE,CASpB;IAED;;;;;OAKG;IACH,oBAJW,MAAM,EAAE,EAAE,GAAC,MAAM,sBAEf,MAAM,EAAE,CAOpB;IAED;;;;;;;;;;OAUG;IACH,kBARW,MAAM,EAAE,GAAC,MAAM;QAEO,mBAAmB,GAAzC,OAAO;QACe,4BAA4B,GAAlD,OAAO;QAEL,MAAM,CAgBlB;IAED;;;;;;;;OAQG;IACH,yBAPW,MAAM,EAAE;QAEc,mBAAmB,GAAzC,OAAO;QACe,4BAA4B,GAAlD,OAAO;QAEL,MAAM,CAiClB;IAED,oCAYC;IAJO,qCAAuC;IAM/C;;;;OAIG;IAEH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA2CG;IACH;;;;cAhDc,MAAM;;;;iBACN,MAAM;;QAiCQ,aAAa,GAA9B,MAAM;QAEY,qBAAqB,GAAvC,OAAO;QAIW,QAAQ,GAA1B,OAAO;QACW,OAAO,GAAzB,OAAO;QACW,UAAU,GAA5B,OAAO;QACU,UAAU,GAA3B,MAAM;QAEY,aAAa,GAA/B,OAAO;QACL,MAAM,GAAG,MAAM,GAAG,MAAM,EAAE,GAAE,MAAM,EAAE,EAAE,CA+ClD;CACJ;AAED;;;GAGG;AACH;CAEC;AACD;;;GAGG;AACH;CAEC;AACD;CAEC;AACD;CAEC;AACD;CAEC;AACD;CAEC;AACD;CAEC;AACD;CAEC;AACD;CAEC;AACD;CAAgE;AAChE;CAA+D;AAC/D;IAGI,sDAGC;CACJ;AACD;CAEC;AAED;CAAwD;AACxD;CAEC;AACD;CAA0D;AAC1D;IACI,sDAMC;IAHG,sBAA0C;IAC1C,sBAAiF;IACjF,+BAA2B;IAG/B;;;;;;OAMG;IACH,sCALW,MAAM,GAAC,MAAM,EAAE,qDAOzB;CACJ;AACD;CAAwD;AAExD;CAA6D;AAE7D;IAEI,sDASC;CACJ;AAID;IAUI,sDAcC;IArBD,8BAKgG;IAI5F,+BAAmF;IAEnF,YAA4C;IAgChD,iCAIC;CACJ;AACD;CAA0D;AAE1D;CAAgE;AAChE;CAA2D;AAE3D;CAA4D;AAE5D;CAA6D;AAE7D;CAAyD;AAqDzD;;;;;;;;;;;;GAYG;AACH;IAEI,sDAMC;IAHG,sBAA+C;IAC/C,sBAAiF;IACjF,+BAA2B;IAG/B;;;;;;OAMG;IACH,sCALW,MAAM,GAAC,MAAM,EAAE,qDAOzB;CACJ;AAED;;;;;;;;;GASG;AACH;IACI,sDAQC;IALG,sBAAuC;IACvC,sBAE6B;IAC7B,kCAAoC;IAGxC;;;;;;OAMG;IACH,sCALW,MAAM,GAAC,MAAM,EAAE,qDAOzB;CACJ;AA6HD;;;GAGG;AACH;IAGI;;;;;OAKG;IACH,uBAJW,MAAM;QAAC,MAAM,EAAE,MAAM,EAAE,CAAC;QAAC,gBAAgB,CAAC,EAAE,MAAM,EAAE,CAAC;QAAC,MAAM,EAAE,MAAM,EAAE,CAAA;KAAC,CAAC;iBAE/C,SAAS,GAAC,MAAM;YAAC,QAAQ,EAAE,MAAM,GAAC,IAAI,CAAC;YAAC,SAAS,EAAE,MAAM,MAAM,GAAC,IAAI,CAAC,CAAC;YAAC,IAAI,EAAE,MAAM,CAAA;SAAC,CAAC;SA4RxH;IAED;;;;;;OAMG;IACH,kCAsFC;IAED,eAAe;IACf,8BAgBC;IAED;;;;;;;;;OASG;IACH,+BAaC;IAwBD;;;;OAIG;IACH,6BA0BC;IAED;;;;;OAKG;IACH,6BAqCC;IAED;;;;OAIG;IACH,4BAoCC;IAED;;;;;;;;OAQG;IACH,0BA+CC;IAED;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACH;QAV4B,QAAQ,GAAzB,MAAM;QAIW,IAAI,GAArB,MAAM;QAGY,aAAa,GAA/B,OAAO;QACL,MAAM,EAAE,EAAE,CA0EtB;CACJ;AACD;CAA6D;AAC7D;CAA0D;AAC1D;CAA4D;AAE5D;;;GAGG;AACH;IASQ,sBAAqC;IAErC,mCAEC;IAKL;;;;;;;OAOG;IACH,mBAHW,MAAM,GAAC,IAAI,SAsBrB;CAEJ;AAED;CAAiE;AAEjE;CAEC;AACD;CAAqE;AAErE;CAA8D;AAE9D;CAA4D;AAE5D;IAEI,sDAKC;CACJ;AACD;;;;;;GAMG;AACH;IACI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MA4CC;IAGD;;;;;;;;;;;;;;OAcG;IACH,sDATW,MAAM,6FAKN,0BAA0B,GAExB,QAAQ,mBAAmB,CAAC,CA+BxC;CACJ;;;;;;;;aA3qIa,OAAO;;yCACR,OAAO,gBAAgB,EAAE,iBAAiB,GAAG,mBAAmB;;;;;WAyiB/D,MAAM;;;;UACN,MAAM;;;;YACN,MAAM;;;;WACN,OAAO;;;;WACP,OAAO;;qCAgzBR,SAAS,GAAC,UAAU,GAAC,oBAAoB,GAAC,gBAAgB,GAAC,YAAY;;;;;YAqGtE,MAAM,EAAE;;;;qBACR,MAAM,EAAE;;;;;;eAMR,MAAM,EAAE;;;;oBACR,MAAM,EAAE;;;;qBACR,MAAM,EAAE;;;;;;AA3oBtB;;;GAGG;AACH;IASI;;;;;;OAMG;IACH,gCAHa,UAAU,CA+BtB;IA3CD;;OAEG;IACH,yBAGC;IADG,YAAoB;IAwCxB;;;;;;OAMG;IACH,gBAJW,MAAM,GACJ,MAAM,CAKlB;IAED;;;;OAIG;IACH,YAHW,MAAM,GACJ,MAAM,CAIlB;CAEJ;;;;;AA+SD;;;;GAIG;AACH;IACI;;;;;;;KAOC;IACD,gCAHW,YAAY,CA6BtB;IAED;;;;;;;;OAQG;IACH,wBALW,MAAM,kBAEJ,MAAM,EAAE,CAKpB;IAED;;;;;OAKG;IACH,mBAJW,MAAM,GAAC,MAAM,EAAE,kBAEb,MAAM,EAAE,CAOpB;IAED;;;;;OAKG;IACH,YAJW,MAAM,GAAC,MAAM,EAAE,kBAEb,MAAM,EAAE,CAIpB;CACJ;;;;;AA8LD;;;;GAIG;AAGH;;;;;GAKG;AAGH;;GAEG;AACH;IAUI;;;;;;OAMG;IACH,gCAHa,aAAa,CAoBzB;IAhCD;;OAEG;IACH,yBAGC;IADG,YAAoB;IA6BxB;;;;;;;OAOG;IACH,6CAHa,mBAAmB,CAK/B;IAED;;;;;OAKG;IACH,sCAFa,mBAAmB,CAI/B;CACJ;;;;;AAwHD;;;GAGG;AACH;IAiBI;;;;;;KAMC;IACD,gCAHW,OAAO,CAgCjB;IAnDD;;;;MAIE;IACF,yBAQC;IANG,YAAoB;IAEpB,2BAA2B;IAC3B,cADW,UAAU,EAAE,CACD;IACtB,wBAA8B;IAC9B,kBAAuC;IAyC3C;;;;;MAKE;IACF,cAHU,MAAM,EAAE,GACN,MAAM,CAIjB;IAED;;;;MAIE;IACF,eAHU,MAAM,EAAE,GACN,MAAM,CAIjB;IAED;;;;;;OAMG;IACH,qBAJW,MAAM,EAAE,GACN,MAAM,EAAE,CAKpB;CAEJ;AAtiDD;;;;;GAKG;AACH;IACI;;;;;;;;;;OAUG;IACH;QAR0B,OAAO,EAAtB,MAAM;QACS,EAAE,EAAjB,MAAM;QACW,WAAW,GAA5B,OAAO;QACU,MAAM,GAAvB,OAAO;QACU,MAAM,GAAvB,OAAO;QACU,UAAU,GAA3B,OAAO;QACU,OAAO,GAAxB,OAAO;OAUjB;IAPG,gBAA6B;IAC7B,WAAmB;IACnB,qBAA8C;IAC9C,gBAAoC;IACpC,gBAAoC;IACpC,iBAAsC;IACtC,oBAA2C;CAElD;uBA/NsB,mBAAmB"}